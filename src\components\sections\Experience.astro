---
import type { Sections } from "@/data/portfolio";
import { Image } from "astro:assets";

interface Props {
    data: Sections["experiences"];
}

const {
    title,
    employments: employmentData,
    qualifications: qualificationData,
    certifications: certificationData,
} = Astro.props.data;

const { title: employmentTitle, employments } = employmentData;
const { title: qualificationTitle, qualifications } = qualificationData;
const { title: certificationTitle, certifications } = certificationData;
---

<section id="experience" aria-labelledby="exp-title" class="py-10 border-t border-slate-200">
    <h2 id="exp-title" class="text-2xl text-sky-900 font-bold">{title}</h2>

    <div class="mt-6 space-y-8">
        <!-- Employment Experience -->
        {
            employments && employments.length > 0 && (
                <section aria-labelledby="employment-title">
                    <h3 id="employment-title" class="text-sky-900 font-bold">
                        {employmentTitle}
                    </h3>
                    <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
                        {employments.map((job) => (
                            <article class="bg-white border border-slate-200 rounded p-4 flex flex-col h-full">
                                <div class="flex max-[320px]:flex-col items-start gap-4">
                                    <div class="bg-slate-100 rounded-md overflow-hidden">
                                        <Image
                                            src={job.logo}
                                            alt=""
                                            width={96}
                                            height={96}
                                            layout="fixed"
                                            class="flex-shrink-0 w-24 h-24"
                                        />
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sky-900 font-bold text-lg mb-1">{job.organization}</h4>
                                        <p class="text-sky-600 italic font-bold mb-2">{job.position}</p>

                                        <div class="text-sm text-slate-600 mb-2 space-y-1">
                                            <p>Period: {job.period}</p>
                                            <p>Location: {job.location}</p>
                                            <p>Employment Type: {job.employmentType}</p>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Academic Qualifications -->
        {
            qualifications && qualifications.length > 0 && (
                <section aria-labelledby="degrees-title" class="border-t border-slate-100 pt-6">
                    <h3 id="degrees-title" class="text-sky-900 font-bold">
                        {qualificationTitle}
                    </h3>
                    <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
                        {qualifications.map((degree) => (
                            <article class="bg-white border border-slate-200 rounded p-4 flex flex-col h-full">
                                <div class="flex max-[320px]:flex-col items-start gap-4">
                                    <div class="bg-slate-100 rounded-md overflow-hidden">
                                        <Image
                                            src={degree.logo}
                                            alt=""
                                            width={96}
                                            height={96}
                                            layout="fixed"
                                            class="flex-shrink-0 w-24 h-24"
                                        />
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sky-900 font-bold text-lg mb-1">{degree.degree}</h4>
                                        <p class="text-sky-600 italic font-bold mb-2">{degree.university}</p>

                                        <div class="text-sm text-slate-600 mb-2 space-y-1">
                                            {degree.yearCompleted && (
                                                <p>
                                                    Year Completed:
                                                    {degree.yearCompleted}
                                                </p>
                                            )}
                                            <p>Type: {degree.type}</p>
                                            {degree.details && (
                                                <p class="text-sm text-slate-600 mt-2">{degree.details}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Professional Certifications -->
        {
            certifications && certifications.length > 0 && (
                <section aria-labelledby="certs-title" class="border-t border-slate-100 pt-6">
                    <h3 id="certs-title" class="text-sky-900 font-bold">
                        {certificationTitle}
                    </h3>
                    <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
                        {certifications.map((cert) => {
                            const IconComponent = cert.icon;
                            return (
                                <article class="bg-white border border-slate-200 rounded p-4 flex flex-col h-full">
                                    <div class="flex max-[320px]:flex-col items-start gap-4">
                                        <div class="flex-shrink-0 mt-1 w-24 h-24 bg-slate-100 rounded-md grid place-items-center">
                                            <IconComponent size={40} class="text-sky-600" />
                                        </div>

                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-sky-900 font-bold text-lg mb-1">{cert.title}</h4>
                                            <p class="text-sky-600 italic font-bold text-base mb-2">{cert.issued}</p>

                                            <div class="text-sm text-slate-600 space-y-1">
                                                <p>Issuer: {cert.issuer}</p>
                                                <p>Location: {cert.location}</p>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            );
                        })}
                    </div>
                </section>
            )
        }
    </div>
</section>
