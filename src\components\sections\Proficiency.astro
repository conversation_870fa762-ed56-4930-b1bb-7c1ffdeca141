---
import { Picture } from "astro:assets";
import type { Sections } from "@/data/portfolio";

interface Props {
    data: Sections["proficiency"];
}

const { title, proficiency } = Astro.props.data;
---

<section id="proficiency" class="py-10 border-t border-slate-200">
    <h2 class="text-2xl text-sky-900 font-bold">{title}</h2>
    <div class="mt-4 grid sm:grid-cols-2 gap-4">
        {
            proficiency.map((item) => (
                <figure class="bg-white border border-slate-200 rounded p-2">
                    <Picture src={item.image} alt={item.alt} width={800} height={480} class="w-full h-auto" />
                    <figcaption class="text-xs text-slate-600 mt-2">{item.description}</figcaption>
                </figure>
            ))
        }
    </div>
</section>
