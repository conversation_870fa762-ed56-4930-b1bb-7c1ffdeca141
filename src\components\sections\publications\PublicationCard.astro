---
import { Picture } from "astro:assets";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { cn } from "@/lib/utils";
import { typeStyles, type Publication } from ".";

interface Props {
    publication: Publication;
    index: number;
    type: "published" | "unpublished";
}

const { publication, index, type } = Astro.props;

const currentStyles = typeStyles[type];
const formattedIndex = `[${index + 1}]`;

let publicationTitle: string | undefined;
const notes = JSON.parse(JSON.stringify(publication.notes));

if (type === "unpublished" && Array.isArray(notes) && notes.length > 1) {
    publicationTitle =
        publication.title +
        notes.reduce((prev, curr, i, arr) => {
            if (i === arr.length - 1) return prev;
            return prev + curr;
        }, "");
    notes.splice(0, notes.length - 1);
}
---

<article class="flex flex-row max-[340px]:flex-col gap-4">
    <!-- Left Side: Index Number and Thumbnail (outside card) -->
    <div class="relative flex-shrink-0 w-24">
        <AspectRatio ratio={210 / 297}>
            <Picture
                src={publication.thumbnail}
                alt="Publication thumbnail"
                width={96}
                widths={[96, 192, publication.thumbnail.width]}
                sizes="96px"
                formats={["avif", "webp"]}
                quality="high"
                priority={index === 0}
                layout="full-width"
            />
        </AspectRatio>
        <!-- Index number overlay on thumbnail -->
        <div
            class={cn(
                "absolute -top-2 -left-2 p-1 rounded-sm text-white grid place-items-center text-sm font-bold shadow-sm",
                currentStyles.bgSecondary,
                currentStyles.textPrimary
            )}
        >
            {formattedIndex}
        </div>
    </div>

    <!-- Right Side: Content Card -->
    <div class="bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full flex flex-col">
        <span class="text-sm text-slate-500 mb-1 break-words" set:html={publication.authors} />
        <h4
            class="text-slate-800 font-bold mb-1 leading-tight break-words"
            set:html={publicationTitle ?? publication.title}
        />
        <span class="text-sm text-slate-600 mb-2 break-words" set:html={publication.journal} />

        <div class="flex flex-wrap items-center gap-2 sm:gap-4 text-sm">
            {
                notes && Array.isArray(notes) && notes.length > 0 && notes[0] !== "" && (
                    <span class="px-2 py-1 bg-slate-100 rounded-sm font-bold">
                        {notes.map((note, index) => (
                            <span
                                class={cn("break-words", index === 0 ? "text-red-600" : "text-green-600")}
                                set:html={note}
                            />
                        ))}
                    </span>
                )
            }
            {
                publication.link && (
                    <a
                        href={publication.link}
                        class={cn("underline break-words", currentStyles.textPrimary, currentStyles.textHover)}
                    >
                        View publication
                    </a>
                )
            }
        </div>
    </div>
</article>
