<script setup lang="ts">
import { ref, computed } from "vue";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";

interface Props {
    publishedPublicationsCount: number;
    unpublishedPublicationsCount: number;
}

const { publishedPublicationsCount, unpublishedPublicationsCount } = defineProps<Props>();

// Centralized tab configuration
const tabConfigs = [
    {
        value: "all",
        label: "All",
    },
    {
        value: "published",
        label: "Published",
        publicationType: "published",
    },
    {
        value: "unpublished",
        label: "Unpublished",
        publicationType: "unpublished",
    },
];

// Computed properties using the centralized configuration
const activeTab = ref(tabConfigs[0].value);

const currentTabConfig = computed(() => tabConfigs.find((config) => config.value === activeTab.value) || tabConfigs[0]);

const currentFilteredPublicationsCount = computed(() => {
    switch (currentTabConfig.value.value) {
        case "published":
            return publishedPublicationsCount;
        case "unpublished":
            return unpublishedPublicationsCount;
        case "all":
        default:
            return publishedPublicationsCount + unpublishedPublicationsCount;
    }
});

const getStatusText = computed(() => {
    const count = currentFilteredPublicationsCount.value;
    const publicationType = currentTabConfig.value.publicationType;
    return `Showing ${count} ${publicationType ?? ""} paper${count !== 1 ? "s" : ""}.`;
});
</script>

<template>
    <Tabs v-model="activeTab" class="w-full">
        <TabsList
            class="grid h-auto w-fit max-[350px]:grid-cols-1 max-[350px]:w-full grid-cols-3 bg-slate-200/30 p-1.5"
        >
            <TabsTrigger
                v-for="config in tabConfigs"
                :key="config.value"
                :value="config.value"
                class="data-[state=active]:bg-white data-[state=active]:text-sky-800 text-slate-600 hover:text-sky-800 transition-colors px-4 py-2 max-[350px]:py-3"
            >
                {{ config.label }}
            </TabsTrigger>
        </TabsList>

        <p class="text-sm text-slate-400 mt-3">{{ getStatusText }}</p>

        <!-- Dynamic Tab Content -->
        <TabsContent
            v-for="config in tabConfigs"
            :key="config.value"
            :value="config.value"
            :class="config.value === 'all' ? 'space-y-10 mt-6' : 'space-y-6 mt-6'"
        >
            <template v-if="config.value === 'all' || config.value === 'published'">
                <div v-if="publishedPublicationsCount > 0">
                    <slot name="publishedPublications" />
                </div>
            </template>

            <template v-if="config.value === 'all' || config.value === 'unpublished'">
                <div v-if="unpublishedPublicationsCount > 0">
                    <slot name="unpublishedPublications" />
                </div>
            </template>
        </TabsContent>
    </Tabs>
</template>
