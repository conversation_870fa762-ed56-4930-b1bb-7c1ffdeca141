export { default as Publications } from "./Publications.astro";

export const typeStyles = {
    published: {
        textPrimary: "text-sky-600",
        textHover: "hover:text-sky-800",
        bgPrimary: "bg-sky-600",
        bgSecondary: "bg-sky-50",
    },
    unpublished: {
        textPrimary: "text-amber-600",
        textHover: "hover:text-amber-800",
        bgPrimary: "bg-amber-600",
        bgSecondary: "bg-amber-50",
    },
};

import type { Sections } from "@/data/portfolio";

export type PublicationSection = Sections["publications"];
export type Publication = PublicationSection["publications"][0];
